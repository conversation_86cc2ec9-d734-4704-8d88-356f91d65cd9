/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100vh;
  background-color: #000000; /* OLED Black */
  color: #ffffff;
  overflow: hidden;
}

/* Main App Container */
.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #000000;
}

/* Header */
.header {
  background-color: #000000;
  border-bottom: 1px solid #333333;
  padding: 1rem 1.5rem;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.app-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.clear-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  border: 1px solid #333333;
  color: #ffffff;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.clear-button:hover {
  background-color: #1a1a1a;
  border-color: #555555;
}

.clear-button:active {
  transform: translateY(1px);
}

/* Messages Container */
.messages-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.messages-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Custom scrollbar */
.messages-list::-webkit-scrollbar {
  width: 6px;
}

.messages-list::-webkit-scrollbar-track {
  background: #000000;
}

.messages-list::-webkit-scrollbar-thumb {
  background: #333333;
  border-radius: 3px;
}

.messages-list::-webkit-scrollbar-thumb:hover {
  background: #555555;
}

/* Message Styles */
.message {
  margin-bottom: 1.5rem;
  display: flex;
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-user {
  justify-content: flex-end;
}

.message-ai {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  min-width: 100px;
}

.message-user .message-content {
  background-color: #1a1a1a;
  border: 1px solid #333333;
  border-radius: 1rem 1rem 0.25rem 1rem;
  padding: 0.75rem 1rem;
}

.message-ai .message-content {
  background-color: #0a0a0a;
  border: 1px solid #2a2a2a;
  border-radius: 1rem 1rem 1rem 0.25rem;
  padding: 0.75rem 1rem;
}

.message-text {
  line-height: 1.5;
  word-wrap: break-word;
  white-space: pre-wrap;
  color: #ffffff;
}

.message-time {
  font-size: 0.75rem;
  color: #888888;
  margin-top: 0.5rem;
  text-align: right;
}

.message-ai .message-time {
  text-align: left;
}

/* Loading Indicator */
.loading-indicator {
  padding: 0.75rem 1rem;
}

.loading-dots {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.loading-dots span {
  width: 6px;
  height: 6px;
  background-color: #666666;
  border-radius: 50%;
  animation: loadingPulse 1.4s infinite ease-in-out;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loadingPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Input Section */
.input-section {
  background-color: #000000;
  border-top: 1px solid #333333;
  padding: 1rem 1.5rem;
  flex-shrink: 0;
}

.input-container {
  display: flex;
  gap: 0.75rem;
  max-width: 1200px;
  margin: 0 auto;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  background-color: #1a1a1a;
  border: 1px solid #333333;
  border-radius: 0.75rem;
  padding: 0.75rem 1rem;
  color: #ffffff;
  font-size: 1rem;
  line-height: 1.5;
  resize: none;
  min-height: 44px;
  max-height: 120px;
  font-family: inherit;
  transition: all 0.2s ease;
}

.message-input:focus {
  outline: none;
  border-color: #555555;
  background-color: #222222;
}

.message-input::placeholder {
  color: #888888;
}

.message-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.send-button {
  background-color: #1a1a1a;
  border: 1px solid #333333;
  border-radius: 0.75rem;
  padding: 0.75rem;
  color: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  height: 44px;
  transition: all 0.2s ease;
}

.send-button:hover:not(:disabled) {
  background-color: #2a2a2a;
  border-color: #555555;
}

.send-button:active:not(:disabled) {
  transform: translateY(1px);
}

.send-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    padding: 0.75rem 1rem;
  }

  .app-title {
    font-size: 1.25rem;
  }

  .clear-button {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }

  .messages-list {
    padding: 0.75rem;
  }

  .message-content {
    max-width: 85%;
  }

  .input-section {
    padding: 0.75rem 1rem;
  }

  .input-container {
    gap: 0.5rem;
  }

  .message-input {
    padding: 0.625rem 0.875rem;
    font-size: 0.9rem;
  }

  .send-button {
    padding: 0.625rem;
    min-width: 40px;
    height: 40px;
  }
}

@media (max-width: 480px) {
  .header-content {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }

  .clear-button {
    align-self: center;
  }

  .message-content {
    max-width: 95%;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .message,
  .loading-dots span,
  .clear-button,
  .send-button,
  .message-input {
    animation: none;
    transition: none;
  }
}

/* Focus indicators for keyboard navigation */
.clear-button:focus-visible,
.send-button:focus-visible,
.message-input:focus-visible {
  outline: 2px solid #666666;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .message-user .message-content {
    border-color: #666666;
  }

  .message-ai .message-content {
    border-color: #666666;
  }

  .message-input {
    border-color: #666666;
  }

  .clear-button,
  .send-button {
    border-color: #666666;
  }
}
