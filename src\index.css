:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Force dark theme */
  color-scheme: dark;
  color: #ffffff;
  background-color: #000000;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #000000;
  color: #ffffff;
  overflow: hidden;
}

/* Remove default button styles */
button {
  border: none;
  background: none;
  font-family: inherit;
  cursor: pointer;
}

/* Remove default textarea styles */
textarea {
  border: none;
  background: none;
  font-family: inherit;
  resize: none;
}

/* Ensure proper text selection colors */
::selection {
  background-color: #333333;
  color: #ffffff;
}

::-moz-selection {
  background-color: #333333;
  color: #ffffff;
}
